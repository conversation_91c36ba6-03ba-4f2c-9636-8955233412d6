"use client"

import { PaymentTable } from "./components/payment-table"
import { mockPayments } from "@/lib/mock-data"
import { useAuth } from "@/context/auth-context" // Added
import { useMemo } from "react" // Added
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert" // Added
import { Terminal } from "lucide-react" // Added

// TODO: Financial Management Features to integrate:
// - Driver Payout Management: (New Page/Section - GET /admin/payouts, POST /admin/payouts/process)
// - Commission Calculation: (Viewable, likely configured backend - GET /admin/settings/commissions)
// - Financial Reporting & Exports: (New Page/Section - GET /admin/reports/financial?type=monthly&export=csv)

// TODO: Fetch real payment data from backend API (e.g., GET /admin/payments or /admin/transactions)
// TODO: Implement pagination, sorting, filtering via API calls
// TODO: Use React Query for data fetching and caching

export default function PaymentManagementPage() {
  const { user } = useAuth() // Added
  const role = user?.role || "regional_operator" // Added
  const region = user?.region // Added

  const filteredPayments = useMemo(() => {
    // Added
    if (role === "admin") return mockPayments
    if (role === "regional_head" && region) {
      return mockPayments.filter((p) => p.region === region)
    }
    // Regional operator has no direct access to payment table data as per snippet
    return []
  }, [role, region])

  if (role === "regional_operator") {
    // Added check for operator
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-semibold">Payment Transactions</h1>
        <Alert>
          <Terminal className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            Regional Operators do not have direct access to financial transaction data. Please escalate payment-related
            inquiries.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-semibold">Payment Transactions {role !== "admin" && region ? `(${region})` : ""}</h1>
      <PaymentTable data={filteredPayments} currentUserRole={role} />
    </div>
  )
}

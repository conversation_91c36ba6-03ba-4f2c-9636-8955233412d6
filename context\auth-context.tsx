"use client"

import { createContext, useContext, useState, type ReactNode, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import type { AuthUser } from "@/types"
import { useToast } from "@/components/ui/use-toast" // Import useToast

interface AuthContextType {
  isAuthenticated: boolean
  user: AuthUser | null
  login: (email: string, password: string) => Promise<boolean> // Updated signature
  logout: () => void
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Mock user data for different roles - WITH PASSWORDS FOR DEMO ONLY
// IMPORTANT: In a real application, NEVER store plaintext passwords.
// Passwords should be securely hashed and validated on the server.
const mockCredentials: Record<string, Omit<AuthUser, "id"> & { password?: string }> = {
  "<EMAIL>": {
    name: "Super Admin",
    email: "<EMAIL>",
    role: "admin",
    password: "password_admin", // DEMO ONLY
  },
  "<EMAIL>": {
    name: "Regional Head (KTM)",
    email: "<EMAIL>",
    role: "regional_head",
    region: "Kathmandu",
    password: "password_head", // DEMO ONLY
  },
  "<EMAIL>": {
    name: "Operator (KTM)",
    email: "<EMAIL>",
    role: "regional_operator",
    region: "Kathmandu",
    password: "password_operator", // DEMO ONLY
  },
  // Add more mock users if needed for other regions or roles
  "<EMAIL>": {
    name: "Regional Head (PKR)",
    email: "<EMAIL>",
    role: "regional_head",
    region: "Pokhara",
    password: "password_head_pkr", // DEMO ONLY
  },
  "<EMAIL>": {
    name: "Operator (PKR)",
    email: "<EMAIL>",
    role: "regional_operator",
    region: "Pokhara",
    password: "password_operator_pkr", // DEMO ONLY
  },
}

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast() // Initialize useToast

  useEffect(() => {
    const token = localStorage.getItem("subhyatraManagementToken")
    const storedUser = localStorage.getItem("subhyatraManagementUser")
    if (token && storedUser) {
      try {
        setUser(JSON.parse(storedUser))
      } catch (e) {
        console.error("Failed to parse stored user data", e)
        localStorage.removeItem("subhyatraManagementToken")
        localStorage.removeItem("subhyatraManagementUser")
      }
    }
    setIsLoading(false)
  }, [])

  const login = useCallback(
    async (email: string, password: string): Promise<boolean> => {
      const potentialUser = mockCredentials[email.toLowerCase()]

      if (potentialUser && potentialUser.password === password) {
        const fullUserData: AuthUser = {
          id: `auth-${Date.now()}`, // Generate a mock ID
          name: potentialUser.name,
          email: potentialUser.email,
          role: potentialUser.role,
          region: potentialUser.region,
        }
        localStorage.setItem("subhyatraManagementToken", "mock-jwt-token") // Simulate token
        localStorage.setItem("subhyatraManagementUser", JSON.stringify(fullUserData))
        setUser(fullUserData)
        router.push("/dashboard")
        toast({
          title: "Login Successful",
          description: `Welcome, ${fullUserData.name}!`,
        })
        return true
      } else {
        toast({
          title: "Login Failed",
          description: "Invalid email or password.",
          variant: "destructive",
        })
        return false
      }
    },
    [router, toast],
  )

  const logout = useCallback(() => {
    localStorage.removeItem("subhyatraManagementToken")
    localStorage.removeItem("subhyatraManagementUser")
    setUser(null)
    router.push("/login")
    toast({
      title: "Logged Out",
      description: "You have been successfully logged out.",
    })
  }, [router, toast])

  return (
    <AuthContext.Provider value={{ isAuthenticated: !!user, user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

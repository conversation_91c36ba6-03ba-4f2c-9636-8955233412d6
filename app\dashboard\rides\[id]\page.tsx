"use client"

import { Label } from "@/components/ui/label"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { mockRides, mockUsers, mockDrivers } from "@/lib/mock-data"
import { usePara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { ArrowLeft, User, Car, MapPin, DollarSign, Clock, BarChart2 } from "lucide-react"
import { cn } from "@/lib/utils"

const getRideStatusBadgeClass = (status: string | undefined) => {
  if (status === "completed") return "bg-green-500 hover:bg-green-600 text-white"
  else if (status === "cancelled_user" || status === "cancelled_driver") return "bg-red-500 hover:bg-red-600 text-white"
  else if (
    status === "in_progress" ||
    status === "accepted" ||
    status === "en_route_pickup" ||
    status === "arrived_pickup"
  )
    return "bg-blue-500 hover:bg-blue-600 text-white"
  else if (status === "requested") return "bg-yellow-500 hover:bg-yellow-600 text-black"
  else if (status === "disputed") return "bg-orange-500 hover:bg-orange-600 text-white"
  return "bg-gray-500 text-white"
}

export default function RideDetailPage() {
  const params = useParams()
  const router = useRouter()
  const rideId = params.id as string

  const ride = mockRides.find((r) => r.id === rideId)
  const user = ride ? mockUsers.find((u) => u.id === ride.userId) : undefined
  const driver = ride && ride.driverId ? mockDrivers.find((d) => d.id === ride.driverId) : undefined

  if (!ride) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <p className="text-xl text-muted-foreground">Ride not found.</p>
        <Button onClick={() => router.back()} variant="outline" className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard/rides">Ride Management</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Ride {ride.id}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold">Ride Details</h1>
        <Button onClick={() => router.back()} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Rides
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">Ride ID: {ride.id}</CardTitle>
              <Badge className={cn("mt-2 capitalize", getRideStatusBadgeClass(ride.status))}>
                {ride.status.replace("_", " ")}
              </Badge>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/rides/${ride.id}/analytics`}>
                <BarChart2 className="mr-2 h-4 w-4" /> View Analytics
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-1">
              <Label className="text-sm text-muted-foreground flex items-center">
                <User className="h-4 w-4 mr-2" />
                User
              </Label>
              <p className="font-medium">{user?.name || ride.userName || "N/A"}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-sm text-muted-foreground flex items-center">
                <Car className="h-4 w-4 mr-2" />
                Driver
              </Label>
              <p className="font-medium">{driver?.name || ride.driverName || "N/A"}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-sm text-muted-foreground flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Fare
              </Label>
              <p className="font-medium">रू {ride.fare.toLocaleString()}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-sm text-muted-foreground flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                Pickup Location
              </Label>
              <p className="font-medium">{ride.pickupLocation}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-sm text-muted-foreground flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                Dropoff Location
              </Label>
              <p className="font-medium">{ride.dropoffLocation}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-sm text-muted-foreground">Ride Type</Label>
              <p className="font-medium capitalize">{ride.rideType}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-sm text-muted-foreground flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Request Time
              </Label>
              <p className="font-medium">{new Date(ride.requestTime).toLocaleString()}</p>
            </div>
            {ride.completionTime && (
              <div className="space-y-1">
                <Label className="text-sm text-muted-foreground flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  Completion Time
                </Label>
                <p className="font-medium">{new Date(ride.completionTime).toLocaleString()}</p>
              </div>
            )}
            <div className="space-y-1">
              <Label className="text-sm text-muted-foreground">Region</Label>
              <p className="font-medium">{ride.region || "N/A"}</p>
            </div>
          </div>
          {/* Placeholder for Map View of the ride path */}
          <Card>
            <CardHeader>
              <CardTitle>Ride Path (Placeholder)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[250px] flex items-center justify-center bg-muted/30 rounded-lg border border-dashed">
                <p className="text-muted-foreground">Map View Placeholder</p>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
      {/* Placeholder for related payments, disputes, etc. */}
    </div>
  )
}

"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useAuth } from "@/context/auth-context"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Wrench, ShieldAlert, Trash2, DatabaseBackup, Briefcase, RotateCcw } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function AdminToolsPage() {
  const { user } = useAuth()
  const { toast } = useToast()

  if (user?.role !== "admin") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to access Admin Tools.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    )
  }

  const handleAdminAction = (actionName: string) => {
    toast({
      title: "Admin Action (Mock)",
      description: `${actionName} initiated. This is a mock action.`,
    })
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Admin Tools</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold flex items-center">
          <Wrench className="mr-3 h-8 w-8 text-primary" />
          Admin Tools
        </h1>
      </div>
      <CardDescription>Access various administrative utilities and system management functions.</CardDescription>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <RotateCcw className="mr-2 h-5 w-5" />
              Cache Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Clear system caches to reflect immediate changes or resolve stale data issues.
            </p>
            <Button onClick={() => handleAdminAction("Force Cache Clear")} className="w-full">
              Clear All Caches
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DatabaseBackup className="mr-2 h-5 w-5" />
              System Backup
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Manually trigger a full system backup. Backups usually run on a schedule.
            </p>
            <Button onClick={() => handleAdminAction("Trigger System Backup")} className="w-full">
              Trigger Backup
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Briefcase className="mr-2 h-5 w-5" />
              Background Jobs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">Monitor and manage background job queues and workers.</p>
            <Button variant="outline" className="w-full bg-background text-foreground">
              View Job Status (Placeholder)
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Trash2 className="mr-2 h-5 w-5 text-destructive" />
              Data Pruning
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Tools for pruning old or irrelevant data (e.g., old logs, soft-deleted records).
            </p>
            <Button
              variant="destructive"
              onClick={() => handleAdminAction("Access Data Pruning Tools")}
              className="w-full"
            >
              Access Pruning Tools
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

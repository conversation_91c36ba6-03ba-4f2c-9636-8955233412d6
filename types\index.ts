import type React from "react"

export type UserRole = "admin" | "regional_head" | "regional_operator"

export interface User {
  id: string
  name: string
  email: string
  phone: string
  registrationDate: string
  status: "active" | "suspended" | "pending_verification"
  profilePictureUrl?: string
  region?: string // Added for regional filtering
  role: UserRole // Add this line
}

export interface Driver {
  id: string
  name: string
  email: string
  phone: string
  licenseNumber: string
  vehicleInfo: string
  registrationDate: string
  status: "approved" | "pending" | "rejected" | "active_online" | "active_offline"
  rating: number
  profilePictureUrl?: string
  region?: string // Added for regional filtering
}

export interface Ride {
  id: string
  userId: string
  userName?: string
  driverId?: string
  driverName?: string
  pickupLocation: string
  dropoffLocation: string
  fare: number
  status:
    | "requested"
    | "accepted"
    | "en_route_pickup"
    | "arrived_pickup"
    | "in_progress"
    | "completed"
    | "cancelled_user"
    | "cancelled_driver"
    | "disputed"
  requestTime: string
  completionTime?: string
  rideType: "bike" | "car" | "auto"
  region?: string // Added for regional filtering
}

export interface Payment {
  id: string
  rideId: string
  userId: string
  userName?: string
  driverId?: string
  driverName?: string
  amount: number
  method: "eSewa" | "Khalti" | "Cash" | "Wallet"
  status: "pending" | "completed" | "failed" | "refunded"
  transactionId: string
  paymentDate: string
  region?: string // Added for regional filtering
}

export interface StatCardData {
  title: string
  value: string
  icon: React.ElementType
  change?: string
  changeType?: "positive" | "negative"
  description?: string
}

// For role-based access control
export interface AuthUser {
  // Define properties of the authenticated admin/staff user
  id: string
  name: string
  email: string
  role: UserRole
  region?: string // Assign a region if the role is regional_head or regional_operator
}

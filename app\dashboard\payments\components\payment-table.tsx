"use client"

import * as React from "react"
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
  useReactTable,
  type ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Eye, RotateCcw } from "lucide-react"
import type { Payment, UserRole } from "@/types" // Import UserRole
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

// Props updated to include currentUserRole
export function PaymentTable({ data, currentUserRole }: { data: Payment[]; currentUserRole: UserRole }) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const { toast } = useToast()

  // Permissions based on role
  const canViewDetails = currentUserRole === "admin" || currentUserRole === "regional_head"
  const canIssueRefund = currentUserRole === "admin" // Only admin can issue refunds directly from here as per snippet (Regional Head has fare adjustment limits on ride page)

  const handleAction = (action: string, paymentId: string) => {
    toast({
      title: `Payment Action: ${action}`,
      description: `Action '${action}' on payment ID '${paymentId}' would be sent to the API. (Mocked for ${currentUserRole})`,
    })
  }

  const columns: ColumnDef<Payment>[] = [
    { accessorKey: "transactionId", header: "Transaction ID" },
    { accessorKey: "rideId", header: "Ride ID" },
    { accessorKey: "userName", header: "User" },
    { accessorKey: "driverName", header: "Driver", cell: ({ row }) => row.original.driverName || "N/A" },
    { accessorKey: "amount", header: "Amount (रू)", cell: ({ row }) => `रू ${row.original.amount.toLocaleString()}` },
    { accessorKey: "method", header: "Method" },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status
        let badgeClass = ""
        if (status === "completed") badgeClass = "bg-green-500 hover:bg-green-600 text-white"
        else if (status === "failed") badgeClass = "bg-red-500 hover:bg-red-600 text-white"
        else if (status === "pending") badgeClass = "bg-yellow-500 hover:bg-yellow-600 text-black"
        else if (status === "refunded") badgeClass = "bg-blue-500 hover:bg-blue-600 text-white"

        return <Badge className={cn("capitalize", badgeClass)}>{status}</Badge>
      },
    },
    {
      accessorKey: "paymentDate",
      header: "Date",
      cell: ({ row }) => new Date(row.original.paymentDate).toLocaleString(),
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const payment = row.original
        if (!canViewDetails && !canIssueRefund) return null // No actions if no permissions

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              {canViewDetails && (
                <DropdownMenuItem onClick={() => handleAction("View Details", payment.id)}>
                  <Eye className="mr-2 h-4 w-4" /> View Details
                </DropdownMenuItem>
              )}
              {canIssueRefund && payment.status === "completed" && (
                <DropdownMenuItem onClick={() => handleAction("Issue Refund", payment.id)} className="text-blue-600">
                  <RotateCcw className="mr-2 h-4 w-4" /> Issue Refund
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  })

  return (
    <div>
      <div className="flex items-center py-4">
        <Input
          placeholder="Filter by Transaction ID..."
          value={(table.getColumn("transactionId")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("transactionId")?.setFilterValue(event.target.value)}
          className="max-w-sm"
        />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button variant="outline" size="sm" onClick={() => table.previousPage()} disabled={!table.getCanPreviousPage()}>
          Previous
        </Button>
        <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
          Next
        </Button>
      </div>
    </div>
  )
}

"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>hadcnSheetTrigger } from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Menu,
  Home,
  Users,
  Car,
  Route,
  CreditCard,
  LogOut,
  CircleUser,
  Settings,
  LifeBuoy,
  FileText,
  PanelRightOpen,
  Shield,
  MapPin,
} from "lucide-react"
import { useAuth } from "@/context/auth-context"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useSidebar } from "@/components/ui/sidebar"
import { useTranslations } from "next-intl"
import { LanguageToggle } from "./language-toggle"

// Duplicating navItems logic here for header's mobile menu, or import from sidebar if structure allows
const allNavItems = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: Home,
    roles: ["admin", "regional_head", "regional_operator"],
    regionalLabel: "Regional Dashboard",
  },
  {
    href: "/dashboard/users",
    label: "User Management",
    icon: Users,
    roles: ["admin", "regional_head", "regional_operator"],
  },
  {
    href: "/dashboard/drivers",
    label: "Driver Management",
    icon: Car,
    roles: ["admin", "regional_head", "regional_operator"],
  },
  {
    href: "/dashboard/rides",
    label: "Ride Management",
    icon: Route,
    roles: ["admin", "regional_head", "regional_operator"],
  },
  { href: "/dashboard/payments", label: "Payments", icon: CreditCard, roles: ["admin", "regional_head"] },
  { href: "/dashboard/reports", label: "Financial Reports", icon: FileText, roles: ["admin", "regional_head"] },
  {
    href: "/dashboard/support",
    label: "Support Tickets",
    icon: LifeBuoy,
    roles: ["admin", "regional_head", "regional_operator"],
  },
  { href: "/dashboard/settings", label: "System Settings", icon: Settings, roles: ["admin"] },
  { href: "/dashboard/regional-settings", label: "Regional Settings", icon: MapPin, roles: ["regional_head"] },
  { href: "/dashboard/admin-tools", label: "Admin Tools", icon: Shield, roles: ["admin"] },
]

export function Header() {
  const t = useTranslations("Header")
  const { logout, user } = useAuth()
  const pathname = usePathname()
  const { toggleSidebar, state: sidebarState, isMobile } = useSidebar()
  const currentUserRole = user?.role || "regional_operator"

  const navItemsForRole = allNavItems.filter((item) => item.roles.includes(currentUserRole))

  // This logic for currentPageLabel might not be strictly necessary in the header
  // if the header is generic and doesn't display the current page name.
  // Keeping it for now if it's used elsewhere or for future header enhancements.
  const currentPageItem = navItemsForRole.find((item) => pathname.startsWith(item.href))
  const currentPageLabel =
    currentUserRole !== "admin" && currentPageItem?.regionalLabel
      ? currentPageItem.regionalLabel
      : currentPageItem?.label || "Dashboard"

  return (
    <header className="flex h-16 items-center gap-4 border-b bg-card px-4 md:px-6 sticky top-0 z-30">
      {!isMobile && sidebarState === "collapsed" && (
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="text-muted-foreground hover:text-foreground"
          aria-label="Expand sidebar"
        >
          <PanelRightOpen className="h-5 w-5" />
        </Button>
      )}

      {isMobile && (
        <Sheet>
          <ShadcnSheetTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="shrink-0 md:hidden border-border hover:bg-secondary"
              aria-label="Open navigation menu"
            >
              <Menu className="h-5 w-5 text-muted-foreground" />
            </Button>
          </ShadcnSheetTrigger>
          <SheetContent side="left" className="flex flex-col p-0 bg-sidebar text-sidebar-foreground md:hidden">
            <div className="flex items-center justify-between h-16 px-4 border-b border-sidebar-border">
              <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
                <Image src="/subhyatra-logo.svg" alt="SubhYatra Logo" width={32} height={32} />
                <span>SubhYatra</span>
              </Link>
            </div>
            <nav className="flex-1 grid gap-1 p-2 text-sm font-medium overflow-y-auto">
              {navItemsForRole.map((item) => {
                const label = currentUserRole !== "admin" && item.regionalLabel ? item.regionalLabel : item.label
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 rounded-md px-3 py-2 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors",
                      pathname.startsWith(item.href)
                        ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
                        : "text-sidebar-foreground",
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                    {label}
                  </Link>
                )
              })}
            </nav>
            <div className="mt-auto border-t border-sidebar-border p-2">
              <Button
                variant="ghost"
                className="w-full justify-start gap-3 px-3 py-2 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                onClick={logout}
              >
                <LogOut className="h-5 w-5" />
                Logout
              </Button>
            </div>
          </SheetContent>
        </Sheet>
      )}

      <div className="w-full flex-1">
        <h1 className="text-lg font-semibold text-foreground">{t("title")}</h1>
      </div>
      <LanguageToggle />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full w-9 h-9 hover:bg-secondary"
            aria-label={t("userMenu")}
          >
            <CircleUser className="h-5 w-5 text-muted-foreground" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="bg-card border-border w-56">
          <DropdownMenuLabel className="text-foreground">
            {user?.name || "User Account"}
            {user?.role && (
              <span className="block text-xs text-muted-foreground capitalize">
                {user.role.replace("_", " ")}
                {user.role !== "admin" && user.region && ` - ${user.region}`}
              </span>
            )}
          </DropdownMenuLabel>
          <DropdownMenuSeparator className="bg-border" />
          <DropdownMenuItem asChild>
            <Link href="/dashboard/account-settings" className="flex items-center w-full cursor-pointer">
              <Settings className="mr-2 h-4 w-4" />
              <span>{t("accountSettings")}</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/dashboard/help-center" className="flex items-center w-full cursor-pointer">
              <LifeBuoy className="mr-2 h-4 w-4" />
              <span>{t("support")}</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator className="bg-border" />
          <DropdownMenuItem
            onClick={logout}
            className="text-destructive-foreground hover:!bg-destructive hover:!text-destructive-foreground focus:!bg-destructive focus:!text-destructive-foreground cursor-pointer"
          >
            <LogOut className="mr-2 h-4 w-4" />
            {t("logout")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </header>
  )
}

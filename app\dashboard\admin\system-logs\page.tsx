"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useAuth } from "@/context/auth-context"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { FileTerminal, ShieldAlert } from "lucide-react"

const mockLogs = [
  {
    id: "log1",
    timestamp: "2023-06-20T10:00:00Z",
    level: "INFO",
    service: "API",
    message: "User login successful: <EMAIL>",
  },
  {
    id: "log2",
    timestamp: "2023-06-20T10:05:00Z",
    level: "WARN",
    service: "PaymentGateway",
    message: "High latency detected for eSewa endpoint.",
  },
  {
    id: "log3",
    timestamp: "2023-06-20T10:10:00Z",
    level: "ERROR",
    service: "Database",
    message: "Failed to connect to replica set.",
  },
  {
    id: "log4",
    timestamp: "2023-06-20T10:15:00Z",
    level: "INFO",
    service: "RideService",
    message: "New ride created: RIDE00X1",
  },
  {
    id: "log5",
    timestamp: "2023-06-20T10:20:00Z",
    level: "DEBUG",
    service: "NotificationService",
    message: "Push notification sent to driver DRV001",
  },
]

export default function SystemLogsPage() {
  const { user } = useAuth()
  const [searchTerm, setSearchTerm] = useState("")
  const [logLevelFilter, setLogLevelFilter] = useState("all")

  if (user?.role !== "admin") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to view system logs.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    )
  }

  const filteredLogs = mockLogs.filter((log) => {
    const matchesSearch =
      log.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.message.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesLevel = logLevelFilter === "all" || log.level.toLowerCase() === logLevelFilter
    return matchesSearch && matchesLevel
  })

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>System Logs</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold flex items-center">
          <FileTerminal className="mr-3 h-8 w-8 text-primary" />
          System Logs
        </h1>
      </div>
      <CardDescription>View and filter system-level logs for monitoring and debugging purposes.</CardDescription>

      <Card>
        <CardHeader>
          <CardTitle>Log Filters</CardTitle>
          <div className="flex flex-col md:flex-row gap-4 mt-2">
            <Input
              placeholder="Search logs (service, message)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
            <Select value={logLevelFilter} onValueChange={setLogLevelFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="debug">Debug</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="warn">Warning</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Service</TableHead>
                <TableHead>Message</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.length > 0 ? (
                filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>{new Date(log.timestamp).toLocaleString()}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 text-xs rounded-full font-semibold ${
                          log.level === "ERROR"
                            ? "bg-red-100 text-red-700"
                            : log.level === "WARN"
                              ? "bg-yellow-100 text-yellow-700"
                              : log.level === "INFO"
                                ? "bg-blue-100 text-blue-700"
                                : "bg-gray-100 text-gray-700"
                        }`}
                      >
                        {log.level}
                      </span>
                    </TableCell>
                    <TableCell>{log.service}</TableCell>
                    <TableCell className="max-w-md truncate">{log.message}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    No logs found matching your criteria.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/auth-context"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { MapPin, ShieldAlert, Percent, DollarSign } from "lucide-react"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"

export default function RegionalSettingsPage() {
  const { toast } = useToast()
  const { user } = useAuth()

  if (user?.role !== "regional_head") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to access Regional Settings.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    )
  }

  const handleSaveChanges = (section: string, details?: any) => {
    let message = `Saving regional settings for ${section}`
    if (details?.limitExceeded) {
      message = `Action for ${section} exceeds limit for your role. Requires Admin approval.`
      toast({ title: "Limit Exceeded", description: message, variant: "destructive" })
      return
    }
    message += ` in ${user?.region || "your region"} is a placeholder. API call would be made here.`
    toast({
      title: `Save ${section} Settings`,
      description: message,
    })
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Regional Settings</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-semibold flex items-center">
        <MapPin className="mr-3 h-8 w-8 text-primary" />
        Regional Settings ({user?.region || "Your Region"})
      </h1>
      <CardDescription>Manage settings specific to your operational region.</CardDescription>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="mr-2 h-5 w-5" />
            Regional Geographic Zones
          </CardTitle>
          <CardDescription>
            Adjust operational zones and service boundaries within {user?.region || "your region"}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center bg-muted/50 rounded-md border border-dashed">
            <p className="text-muted-foreground">Map Placeholder for Regional Zone Management</p>
          </div>
          <Button onClick={() => handleSaveChanges("Regional Geo Zones")} className="mt-4">
            Save Regional Zones
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Percent className="mr-2 h-5 w-5" />
            Regional Promotions
          </CardTitle>
          <CardDescription>
            Create and manage promotional campaigns for {user?.region || "your region"} (up to $200 limit per campaign).
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="promoName">Promotion Name</Label>
            <Input id="promoName" placeholder="e.g., Summer Discount Pokhara" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="promoValue">Promotion Value ($)</Label>
            <Input id="promoValue" type="number" placeholder="e.g., 150" />
          </div>
          <Button
            onClick={() => {
              const promoValueInput = document.getElementById("promoValue") as HTMLInputElement
              const promoValue = promoValueInput ? Number.parseFloat(promoValueInput.value) : 0
              handleSaveChanges("Regional Promotions", { limitExceeded: promoValue > 200, amount: promoValue })
            }}
          >
            Create Regional Promotion
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="mr-2 h-5 w-5" />
            Regional Fare Adjustments
          </CardTitle>
          <CardDescription>
            Set local fare modifiers or surcharges for {user?.region || "your region"} (Admin approval may be required
            for significant changes).
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="fareModifier">Fare Modifier (%)</Label>
            <Input id="fareModifier" type="number" placeholder="e.g., 5 for +5%, -2 for -2%" />
          </div>
          <div className="flex items-center space-x-2">
            <Switch id="peakHourSurcharge" />
            <Label htmlFor="peakHourSurcharge">Enable Regional Peak Hour Surcharge</Label>
          </div>
          <Button onClick={() => handleSaveChanges("Regional Fare Adjustments")}>Apply Fare Adjustments</Button>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useLocale } from "next-intl"
import { useRouter, usePathname } from "@/lib/navigation"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import { Languages } from "lucide-react"

export function LanguageToggle() {
  const router = useRouter()
  const pathname = usePathname()
  const locale = useLocale()

  const changeLocale = (newLocale: string) => {
    router.replace(pathname, { locale: newLocale })
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="rounded-full w-9 h-9 hover:bg-secondary">
          <Languages className="h-5 w-5 text-muted-foreground" />
          <span className="sr-only">Change language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => changeLocale("en")} disabled={locale === "en"}>
          English
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => changeLocale("ne")} disabled={locale === "ne"}>
          नेपाली
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

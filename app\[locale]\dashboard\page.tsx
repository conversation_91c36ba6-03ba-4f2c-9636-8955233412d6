"use client"

import type React from "react"

import { useTranslations } from "next-intl"
import { StatCard } from "@/components/stat-card"
import { DollarSign, Users, Car, Route } from "lucide-react"

interface StatCardData {
  title: string
  value: string
  icon: React.ComponentType
  change: string
  changeType: "positive" | "negative"
}

export default function DashboardPage() {
  const t = useTranslations("Dashboard")

  const statCards: StatCardData[] = [
    {
      title: t("totalRevenue"),
      value: "रू 5,42,320",
      icon: DollarSign,
      change: "+20.1%",
      changeType: "positive",
    },
    {
      title: t("totalRides"),
      value: "+2350",
      icon: Route,
      change: "+180.1%",
      changeType: "positive",
    },
    {
      title: t("activeDrivers"),
      value: "+573",
      icon: Car,
      change: "+19%",
      changeType: "positive",
    },
    {
      title: t("newUsers"),
      value: "+12,234",
      icon: Users,
      change: "+2.2%",
      changeType: "positive",
    },
  ]

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold">{t("title")}</h1>
      <p className="text-muted-foreground">{t("description")}</p>

      <div className="grid grid-cols-1 gap-4 mt-8 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((card, index) => (
          <StatCard key={index} {...card} />
        ))}
      </div>
    </div>
  )
}

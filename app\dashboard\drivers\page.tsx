"use client"

import { useState, useMemo } from "react"
import { DriverTable } from "./components/driver-table"
import { mockDrivers as initialMockDrivers } from "@/lib/mock-data"
import { Button } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { AddDriverForm } from "./components/add-driver-form"
import type { Driver } from "@/types"
import { useAuth } from "@/context/auth-context"

export default function DriverManagementPage() {
  const { toast } = useToast()
  const { user } = useAuth()
  const role = user?.role || "regional_operator"
  const region = user?.region

  const [driversData, setDriversData] = useState<Driver[]>(initialMockDrivers)
  const [isAddDriverModalOpen, setIsAddDriverModalOpen] = useState(false)

  const filteredDrivers = useMemo(() => {
    if (role === "admin") return driversData
    if ((role === "regional_head" || role === "regional_operator") && region)
      return driversData.filter((d) => d.region === region)
    return []
  }, [driversData, role, region])

  const handleDriverAdded = (newDriver: Driver) => {
    const driverWithRegion = {
      ...newDriver,
      region: role === "admin" ? newDriver.region || "DefaultRegion" : region || newDriver.region || "DefaultRegion",
    }
    setDriversData((prevDrivers) => [driverWithRegion, ...prevDrivers])
    toast({
      title: "Driver Added",
      description: `${newDriver.name} has been successfully added.`,
    })
  }

  const canAddDriver = role === "admin" || role === "regional_head" // Regional operators cannot add drivers

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Driver Management {role !== "admin" && region ? `(${region})` : ""}</h1>
        {canAddDriver && (
          <Button
            onClick={() => setIsAddDriverModalOpen(true)}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            <PlusCircle className="mr-2 h-4 w-4" /> Add Driver
          </Button>
        )}
      </div>
      <DriverTable data={filteredDrivers} currentUserRole={role} />
      {canAddDriver && (
        <AddDriverForm
          open={isAddDriverModalOpen}
          onOpenChange={setIsAddDriverModalOpen}
          onDriverAdded={handleDriverAdded}
        />
      )}
    </div>
  )
}

"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useAuth } from "@/context/auth-context"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Banknote, ShieldAlert } from "lucide-react"

export default function RegionalBudgetAllocationPage() {
  const { user } = useAuth()

  if (user?.role !== "regional_head") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to view Regional Budget Allocation.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Regional Budget Allocation</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold flex items-center">
          <Banknote className="mr-3 h-8 w-8 text-primary" />
          Budget Allocation ({user?.region || "Your Region"})
        </h1>
      </div>
      <CardDescription>
        Manage and track budget allocation for marketing, operations, and other expenses within your region.
      </CardDescription>

      <Card>
        <CardHeader>
          <CardTitle>Budget Overview (Placeholder)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center bg-muted/30 rounded-lg border border-dashed">
            <p className="text-muted-foreground text-center">
              Regional budget details, spending tracking, and allocation tools for {user?.region} will be here.
              <br />
              (e.g., Marketing budget, operational expenses, incentive programs)
            </p>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            This page is a placeholder. A full implementation would integrate with financial systems to display current
            budget status, allow for allocation requests (with approval workflows), and track spending against targets.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState, type FormEvent } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { Driver } from "@/types"

interface AddDriverFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onDriverAdded: (newDriver: Driver) => void
}

export function AddDriverForm({ open, onOpenChange, onDriverAdded }: AddDriverFormProps) {
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [licenseNumber, setLicenseNumber] = useState("")
  const [vehicleInfo, setVehicleInfo] = useState("")
  const [status, setStatus] = useState<Driver["status"]>("pending")

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    const newDriver: Driver = {
      id: `driver${Date.now()}`, // Simple unique ID for mock
      name,
      email,
      phone,
      licenseNumber,
      vehicleInfo,
      registrationDate: new Date().toISOString().split("T")[0],
      status,
      rating: 0, // New drivers start with 0 rating
      profilePictureUrl: "/placeholder.svg?width=40&height=40&text=New",
    }
    onDriverAdded(newDriver)
    onOpenChange(false) // Close dialog
    // Reset form
    setName("")
    setEmail("")
    setPhone("")
    setLicenseNumber("")
    setVehicleInfo("")
    setStatus("pending")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Driver</DialogTitle>
          <DialogDescription>Fill in the details for the new driver application.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="driver-name" className="text-right">
              Name
            </Label>
            <Input
              id="driver-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="driver-email" className="text-right">
              Email
            </Label>
            <Input
              id="driver-email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="driver-phone" className="text-right">
              Phone
            </Label>
            <Input
              id="driver-phone"
              type="tel"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="licenseNumber" className="text-right">
              License No.
            </Label>
            <Input
              id="licenseNumber"
              value={licenseNumber}
              onChange={(e) => setLicenseNumber(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="vehicleInfo" className="text-right">
              Vehicle Info
            </Label>
            <Input
              id="vehicleInfo"
              placeholder="e.g., Bike - BA 2 PA 1234"
              value={vehicleInfo}
              onChange={(e) => setVehicleInfo(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="driver-status" className="text-right">
              Status
            </Label>
            <Select value={status} onValueChange={(value: Driver["status"]) => setStatus(value)}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="active_online">Active (Online)</SelectItem>
                <SelectItem value="active_offline">Active (Offline)</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" className="bg-background text-foreground">
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" className="bg-primary text-primary-foreground hover:bg-primary/90">
              Add Driver
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

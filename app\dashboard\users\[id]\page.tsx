"use client"

import { Label } from "@/components/ui/label"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { mockUsers } from "@/lib/mock-data" // Assuming mock data is available
import { useParams, useRouter } from "next/navigation"
import Link from "next/link"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { ArrowLeft, Edit, Activity, History } from "lucide-react"
import { cn } from "@/lib/utils"

const getUserStatusBadgeClass = (status: string | undefined) => {
  switch (status) {
    case "active":
      return "bg-status-green text-status-green-foreground"
    case "suspended":
      return "bg-status-red text-status-red-foreground"
    case "pending_verification":
      return "bg-status-yellow text-status-yellow-foreground"
    default:
      return "bg-status-gray text-status-gray-foreground"
  }
}

export default function UserDetailPage() {
  const params = useParams()
  const router = useRouter()
  const userId = params.id as string

  // In a real app, fetch user data by ID
  const user = mockUsers.find((u) => u.id === userId)

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <p className="text-xl text-muted-foreground">User not found.</p>
        <Button onClick={() => router.back()} variant="outline" className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard/users">User Management</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{user.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold">User Details</h1>
        <Button onClick={() => router.back()} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Users
        </Button>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-start gap-4 space-y-0">
          <Avatar className="h-20 w-20">
            <AvatarImage
              src={user.profilePictureUrl || "/placeholder.svg?width=80&height=80&text=User"}
              alt={user.name}
            />
            <AvatarFallback>{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <CardTitle className="text-2xl">{user.name}</CardTitle>
            <CardDescription>ID: {user.id}</CardDescription>
            <Badge className={cn("mt-2 capitalize", getUserStatusBadgeClass(user.status))}>
              {user.status.replace("_", " ")}
            </Badge>
          </div>
          <div>
            {/* Placeholder for Edit button, link to /edit page */}
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/users/${user.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" /> Edit
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm text-muted-foreground">Email</Label>
              <p className="font-medium">{user.email}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Phone</Label>
              <p className="font-medium">{user.phone}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Registration Date</Label>
              <p className="font-medium">{new Date(user.registrationDate).toLocaleDateString()}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Region</Label>
              <p className="font-medium">{user.region || "N/A"}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" /> Recent Activity
            </CardTitle>
            <CardDescription>Placeholder for user's recent activity log.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">No activity to display (Placeholder).</p>
            {/* Link to full activity page */}
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link href={`/dashboard/users/${user.id}/activity`}>View All Activity</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5 text-primary" /> Ride History
            </CardTitle>
            <CardDescription>Placeholder for user's ride history.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">No ride history to display (Placeholder).</p>
            {/* Link to full ride history page */}
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link href={`/dashboard/users/${user.id}/rides`}>View All Rides</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
      {/* Placeholder for other sections like Payment History, Support Tickets by User etc. */}
    </div>
  )
}

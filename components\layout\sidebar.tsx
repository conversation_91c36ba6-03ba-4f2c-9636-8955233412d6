"use client"

import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import {
  Home,
  Users,
  Car,
  Route,
  CreditCard,
  LogOut,
  Settings,
  LifeBuoy,
  FileText,
  PanelLeftOpen,
  MapPin,
  ListChecks,
  BarChartBig,
  Banknote,
  FileTerminal,
  Wrench,
  History,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/context/auth-context"
import { cn } from "@/lib/utils"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import type { UserRole } from "@/types"
import React from "react"

interface NavItem {
  href: string
  label: string
  icon: React.ElementType
  roles: UserRole[]
  regionalLabel?: string
  group: string
}

const allNavItems: NavItem[] = [
  // Overview
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: Home,
    roles: ["admin", "regional_head", "regional_operator"],
    regionalLabel: "Regional Dashboard",
    group: "Overview",
  },
  // Core Management
  {
    href: "/dashboard/users",
    label: "User Management",
    icon: Users,
    roles: ["admin", "regional_head", "regional_operator"],
    group: "Core Management",
  },
  {
    href: "/dashboard/drivers",
    label: "Driver Management",
    icon: Car,
    roles: ["admin", "regional_head", "regional_operator"],
    group: "Core Management",
  },
  {
    href: "/dashboard/rides",
    label: "Ride Management",
    icon: Route,
    roles: ["admin", "regional_head", "regional_operator"],
    group: "Core Management",
  },
  // Operations & Finance
  {
    href: "/dashboard/payments",
    label: "Payments",
    icon: CreditCard,
    roles: ["admin", "regional_head"],
    group: "Operations & Finance",
  },
  {
    href: "/dashboard/reports",
    label: "Financial Reports",
    icon: FileText,
    roles: ["admin", "regional_head"],
    group: "Operations & Finance",
  },
  {
    href: "/dashboard/support",
    label: "Support Tickets",
    icon: LifeBuoy,
    roles: ["admin", "regional_head", "regional_operator"],
    group: "Operations & Finance",
  },
  // System Administration (Admin)
  {
    href: "/dashboard/settings", // This will be System Settings for Admin
    label: "System Settings",
    icon: Settings,
    roles: ["admin"],
    group: "System Administration",
  },
  {
    href: "/dashboard/admin-tools",
    label: "Admin Tools",
    icon: Wrench,
    roles: ["admin"],
    group: "System Administration",
  },
  {
    href: "/dashboard/admin/system-logs",
    label: "System Logs",
    icon: FileTerminal,
    roles: ["admin"],
    group: "System Administration",
  },
  {
    href: "/dashboard/admin/audit-trails",
    label: "Audit Trails",
    icon: History,
    roles: ["admin"],
    group: "System Administration",
  },
  // Regional Management (Regional Head)
  {
    href: "/dashboard/regional-head/team",
    label: "Team Management",
    icon: ListChecks,
    roles: ["regional_head"],
    group: "Regional Management",
  },
  {
    href: "/dashboard/regional-settings", // Distinct from System Settings
    label: "Regional Settings",
    icon: MapPin,
    roles: ["regional_head"],
    group: "Regional Management",
  },
  {
    href: "/dashboard/regional-head/performance-analysis",
    label: "Performance Analysis",
    icon: BarChartBig,
    roles: ["regional_head"],
    group: "Regional Management",
  },
  {
    href: "/dashboard/regional-head/budget-allocation",
    label: "Budget Allocation",
    icon: Banknote,
    roles: ["regional_head"],
    group: "Regional Management",
  },
]

const groupOrderConfig: Record<UserRole, string[]> = {
  admin: ["Overview", "Core Management", "Operations & Finance", "System Administration"],
  regional_head: ["Overview", "Core Management", "Operations & Finance", "Regional Management"],
  regional_operator: ["Overview", "Core Management", "Operations & Finance"],
}

export function AppSidebar() {
  const pathname = usePathname()
  const { logout, user } = useAuth()
  const { state: sidebarState, toggleSidebar } = useSidebar()

  const currentUserRole = user?.role || "regional_operator"
  const currentGroupOrder = groupOrderConfig[currentUserRole] || []

  const navItemsForRole = allNavItems.filter((item) => item.roles.includes(currentUserRole))

  const groupedNavItems = currentGroupOrder.reduce(
    (acc, groupName) => {
      const itemsInGroup = navItemsForRole.filter((item) => item.group === groupName)
      if (itemsInGroup.length > 0) {
        acc[groupName] = itemsInGroup
      }
      return acc
    },
    {} as Record<string, NavItem[]>,
  )

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader className="flex items-center justify-between h-16 px-4">
        <Link
          href="/dashboard"
          className={cn(
            "flex items-center gap-2 font-semibold transition-opacity duration-300",
            sidebarState === "collapsed" ? "w-full justify-center" : "",
          )}
        >
          <Image src="/subhyatra-logo.svg" alt="SubhYatra Logo" width={32} height={32} />
          {sidebarState === "expanded" && <span className="text-sidebar-foreground">SubhYatra</span>}
        </Link>
        {sidebarState === "expanded" && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="ml-auto text-sidebar-foreground hover:text-sidebar-accent-foreground hover:bg-sidebar-accent hidden md:flex"
            aria-label="Collapse sidebar"
          >
            <PanelLeftOpen className="h-5 w-5" />
          </Button>
        )}
      </SidebarHeader>
      <SidebarContent>
        {Object.entries(groupedNavItems).map(([groupName, itemsInGroup], groupIndex) => (
          <React.Fragment key={groupName}>
            {sidebarState === "expanded" && (
              <div className="px-4 pt-4 pb-1">
                <h4 className="text-xs font-semibold uppercase text-sidebar-foreground/60 tracking-wider">
                  {groupName}
                </h4>
              </div>
            )}
            {sidebarState === "collapsed" && groupIndex > 0 && (
              <Separator className="my-2 bg-sidebar-border/30 mx-auto w-3/4" />
            )}
            <SidebarMenu className={cn("px-2", sidebarState === "expanded" ? "mt-1" : "mt-2")}>
              {itemsInGroup.map((item) => {
                const label =
                  (currentUserRole === "regional_head" || currentUserRole === "regional_operator") && item.regionalLabel
                    ? item.regionalLabel
                    : item.label
                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton
                      asChild
                      isActive={
                        pathname === item.href || (pathname.startsWith(item.href) && item.href !== "/dashboard")
                      }
                      tooltip={{
                        children: label,
                        side: "right",
                        align: "center",
                        className: "bg-card text-card-foreground border-border",
                      }}
                      className={cn(
                        "text-sidebar-foreground hover:text-sidebar-accent-foreground hover:bg-sidebar-accent",
                        "data-[active=true]:bg-primary data-[active=true]:text-primary-foreground data-[active=true]:hover:bg-primary/90",
                        sidebarState === "collapsed" ? "justify-center" : "",
                      )}
                    >
                      <Link href={item.href} className="flex items-center gap-3 py-2 px-2">
                        <item.icon className="h-5 w-5 shrink-0" />
                        {sidebarState === "expanded" && <span className="truncate">{label}</span>}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </React.Fragment>
        ))}
      </SidebarContent>
      <SidebarFooter className="p-2">
        <SidebarMenu className="px-2">
          <SidebarMenuItem>
            <SidebarMenuButton
              onClick={logout}
              tooltip={{
                children: "Logout",
                side: "right",
                align: "center",
                className: "bg-card text-card-foreground border-border",
              }}
              className={cn(
                "text-sidebar-foreground hover:text-sidebar-accent-foreground hover:bg-sidebar-accent w-full",
                sidebarState === "collapsed" ? "justify-center" : "",
              )}
            >
              <LogOut className="h-5 w-5 shrink-0" />
              {sidebarState === "expanded" && <span className="truncate">Logout</span>}
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}

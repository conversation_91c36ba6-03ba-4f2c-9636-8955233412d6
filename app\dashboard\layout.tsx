"use client"

import React from "react"
import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/auth-context"
import { AppSidebar } from "@/components/layout/sidebar" // Renamed for clarity
import { Header } from "@/components/layout/header"
import { SidebarProvider, SidebarInset, useSidebar } from "@/components/ui/sidebar" // Import new sidebar components
import { cn } from "@/lib/utils"
import { parseCookies } from "nookies" // For reading cookies on client

function DashboardMainContent({ children }: { children: React.ReactNode }) {
  const { open: sidebarOpen, isMobile } = useSidebar()

  // Dynamic grid columns based on sidebar state for desktop
  // On mobile, sidebar is an overlay, so main content takes full width
  const gridColsClass = isMobile
    ? "grid-cols-1" // Mobile: single column, sidebar overlays
    : sidebarOpen
      ? "md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr]" // Desktop: sidebar open
      : "md:grid-cols-[var(--sidebar-width-icon)_1fr] lg:grid-cols-[var(--sidebar-width-icon)_1fr]" // Desktop: sidebar collapsed

  return (
    <div className={cn("grid min-h-screen w-full", gridColsClass)}>
      <AppSidebar />
      <div className="flex flex-col">
        <Header />
        <SidebarInset>
          {" "}
          {/* Manages padding/margin if sidebar variant="inset" is used, or just acts as main content wrapper */}
          <main className="flex flex-1 flex-col gap-4 p-4 lg:gap-6 lg:p-6 bg-background overflow-auto">{children}</main>
        </SidebarInset>
      </div>
    </div>
  )
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // Read sidebar state from cookie for initial open state
  // This needs to be client-side as document.cookie is not available on server for initial render of client component
  const [defaultSidebarOpen, setDefaultSidebarOpen] = React.useState(true)

  React.useEffect(() => {
    const cookies = parseCookies()
    const sidebarStateCookie = cookies["sidebar:state"]
    if (sidebarStateCookie) {
      setDefaultSidebarOpen(sidebarStateCookie === "true")
    }
  }, [])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login")
    }
  }, [isAuthenticated, isLoading, router])

  if (isLoading || !isAuthenticated) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  return (
    <SidebarProvider defaultOpen={defaultSidebarOpen}>
      <DashboardMainContent>{children}</DashboardMainContent>
    </SidebarProvider>
  )
}

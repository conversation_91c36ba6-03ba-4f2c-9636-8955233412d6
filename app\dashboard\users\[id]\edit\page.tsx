"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { useAuth } from "@/context/auth-context"
import { useToast } from "@/components/ui/use-toast"
import { mockUsers } from "@/lib/mock-data"
import type { User } from "@/types"
import { ArrowLeft, Save, Shield<PERSON>lert } from "lucide-react"
import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert"

export default function EditUserPage() {
  const params = useParams()
  const router = useRouter()
  const { user: authUser } = useAuth()
  const { toast } = useToast()
  const userId = params.id as string

  const [userData, setUserData] = useState<Partial<User>>({})
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const userToEdit = mockUsers.find((u) => u.id === userId)
    if (userToEdit) {
      // Check if authUser (Regional Head) can edit this user (must be in their region)
      if (authUser?.role === "regional_head" && userToEdit.region !== authUser.region) {
        toast({
          variant: "destructive",
          title: "Access Denied",
          description: "You can only edit users in your own region.",
        })
        router.push(`/dashboard/users/${userId}`) // Redirect back to detail view
        return
      }
      setUserData(userToEdit)
    } else {
      toast({ variant: "destructive", title: "Error", description: "User not found." })
      router.push("/dashboard/users")
    }
    setIsLoading(false)
  }, [userId, router, toast, authUser])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setUserData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: keyof User, value: string) => {
    setUserData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, send PUT request to /api/users/:id
    toast({
      title: "User Updated (Mock)",
      description: `${userData.name}'s details have been updated.`,
    })
    router.push(`/dashboard/users/${userId}`)
  }

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading user data...</div>
  }

  if (!userData.id) {
    // Handles case where user is not found or access denied by effect
    return null // Effect handles redirection and toast
  }

  const canEditUser =
    authUser?.role === "admin" || (authUser?.role === "regional_head" && userData.region === authUser.region)

  if (!canEditUser) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to edit this user.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href={`/dashboard/users/${userId}`}>Back to User Details</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard/users">Users</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href={`/dashboard/users/${userId}`}>{userData.name || "User"}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Edit</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold">Edit User: {userData.name}</h1>
        <Button onClick={() => router.back()} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Cancel
        </Button>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>Modify the user's details below.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="name">Name</Label>
                <Input id="name" name="name" value={userData.name || ""} onChange={handleInputChange} required />
              </div>
              <div className="space-y-1">
                <Label htmlFor="email">Email</Label>
                <Input id="email" name="email" type="email" value={userData.email || ""} disabled />
                <p className="text-xs text-muted-foreground">Email cannot be changed.</p>
              </div>
              <div className="space-y-1">
                <Label htmlFor="phone">Phone</Label>
                <Input id="phone" name="phone" value={userData.phone || ""} onChange={handleInputChange} required />
              </div>
              <div className="space-y-1">
                <Label htmlFor="status">Status</Label>
                <Select
                  name="status"
                  value={userData.status || ""}
                  onValueChange={(value) => handleSelectChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="pending_verification">Pending Verification</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-1">
                <Label htmlFor="region">Region</Label>
                <Input
                  id="region"
                  name="region"
                  value={userData.region || ""}
                  onChange={handleInputChange}
                  disabled={authUser?.role === "regional_head"}
                />
                {authUser?.role === "regional_head" && (
                  <p className="text-xs text-muted-foreground">Region is fixed for Regional Heads.</p>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="bg-primary text-primary-foreground hover:bg-primary/90">
              <Save className="mr-2 h-4 w-4" /> Save Changes
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
